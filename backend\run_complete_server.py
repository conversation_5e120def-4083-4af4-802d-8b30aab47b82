#!/usr/bin/env python
"""
Complete BizTrend Forecast Server
Runs Django with ASGI support for:
- HTTP API requests
- Media file serving (profile pictures)
- Real-time WebSocket connections
"""
import os
import sys
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    # Set the Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auth.settings')
    
    print("🚀 Starting BizTrend Forecast Complete Server...")
    print("=" * 60)
    print("✅ HTTP API: http://127.0.0.1:8000")
    print("✅ Media Files: http://127.0.0.1:8000/media/")
    print("✅ WebSocket: ws://127.0.0.1:8000/ws/predictions/")
    print("✅ Admin Panel: http://127.0.0.1:8000/admin/")
    print("=" * 60)
    print("🖼️  Profile pictures will display correctly")
    print("📡 Real-time updates are enabled")
    print("🔄 WebSocket connections supported")
    print("=" * 60)
    
    # Use Django's runserver with ASGI support
    # This handles BOTH HTTP requests AND WebSocket connections
    sys.argv = ['manage.py', 'runserver', '127.0.0.1:8000', '--skip-checks']
    execute_from_command_line(sys.argv)
