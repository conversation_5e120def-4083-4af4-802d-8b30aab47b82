# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class AuthScramRequest(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthScramRequest()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAuthScramRequest(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # AuthScramRequest
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AuthScramRequest
    def Nonce(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # AuthScramRequest
    def ChannelBinding(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

def AuthScramRequestStart(builder): builder.StartObject(2)
def Start(builder):
    return AuthScramRequestStart(builder)
def AuthScramRequestAddNonce(builder, nonce): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(nonce), 0)
def AddNonce(builder, nonce):
    return AuthScramRequestAddNonce(builder, nonce)
def AuthScramRequestAddChannelBinding(builder, channelBinding): builder.PrependUint8Slot(1, channelBinding, 0)
def AddChannelBinding(builder, channelBinding):
    return AuthScramRequestAddChannelBinding(builder, channelBinding)
def AuthScramRequestEnd(builder): return builder.EndObject()
def End(builder):
    return AuthScramRequestEnd(builder)