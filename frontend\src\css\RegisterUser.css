.register-user-modal {
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;

  position: fixed;
  z-index: 10;
}

.register-user-modal .overlay {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  position: fixed;
  top: 0;
}

.register-user-modal .content {
  display: flex;
  flex-direction: column;
  height: 70vh;
  width: 40%;
  border-radius: 40px;
  background-color: var(--primary-color);
  box-shadow: 0 0 0 5px rgb(245, 245, 249, 0.5),
    0 0 0 10px rgb(245, 245, 249, 0.5);
  position: absolute;

  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  gap: 10px;
}

.register-user-modal .content header {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  padding: 20px;

  border-bottom: 1px solid #d3d3d3;
}

.register-user-modal .content button:first-child {
  display: flex;
  height: 30px;
  width: 30px;
  padding: 5px;

  border-radius: 50%;

  background-color: var(--primary-color);
  border: 1px solid #d3d3d3;

  transition: 0.5s ease;
  cursor: pointer;
}

.register-user-modal .content button:first-child:hover {
  background-color: #808080;
}

.register-user-modal .content header h2 {
  color: var(--secondary-dark-color);
}

.register-user-modal .content form {
  padding: 20px;
  margin-bottom: 25px;

  gap: 5px;
  overflow: auto;
}

.register-user-modal .content form fieldset:last-of-type {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;

  color: var(--btn-color);
}

.register-user-modal .content form fieldset:last-of-type label {
  font-size: 0.75rem;
  color: var(--btn-color);
}

.register-user-modal span {
  font-size: 0.875rem;
  color: red;
}
