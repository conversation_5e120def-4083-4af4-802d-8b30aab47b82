.upload-dataset-modal {
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;

  position: fixed;
  z-index: 10;
}

.upload-dataset-modal .overlay {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  position: fixed;
  top: 0;
}

.upload-dataset-modal .content {
  display: flex;
  flex-direction: column;
  height: fit-content;
  width: 40%;
  padding: 20px;
  border-radius: 40px;
  background-color: var(--primary-color);
  border: 5px solid #d3d3d3;
  box-shadow: 0 0 0 5px rgb(245, 245, 249, 0.7);
  position: absolute;
  gap: 20px;

  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.upload-dataset-modal .content button:first-child {
  display: flex;
  height: 30px;
  width: 30px;
  padding: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 1px solid #d3d3d3;
  transition: 0.5s ease;
  position: absolute;
  right: 20px;
  cursor: pointer;
}

.upload-dataset-modal .content button:first-child:hover {
  background-color: #808080;
}

.upload-dataset-modal .content h2 {
  color: var(--secondary-dark-color);
}

.upload-dataset-modal .content .reminder {
  font-weight: 400;
}

.upload-dataset-modal .content section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: auto;
  width: 100%;
  border: 2px dashed #d3d3d3;
  border-radius: 40px;
  background-color: var(--primary-color);
  transition: 0.5s ease;
}

.upload-dataset-modal .content section:hover {
  background-color: white;
  border: 2px dashed var(--btn-color);
}

.upload-dataset-modal .upload-icon {
  height: 40px;
  background-color: rgba(79, 70, 229, 0.1);
  padding: 10px;
  border-radius: 10px;
}

.upload-dataset-modal label {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  align-items: center;
  cursor: pointer;
  border-radius: 40px;
  padding: 40px 50px;
  gap: 10px;
}

.upload-dataset-modal label .selected-file {
  display: flex;
  align-items: center;
  max-width: 100%;
  height: 2.5rem;
  padding-left: 10px;
  border-radius: 40px;
  background-color: white;
  border: 1px solid #d3d3d3;
  margin-top: 5px;
  gap: 5px;
}

.upload-dataset-modal label .selected-file p {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.875rem;
}

.upload-dataset-modal .remove-icon {
  height: 100%;
  padding: 2px;
  padding: 12px;
  border-radius: 0px 40px 40px 0px;
  transition: 0.5s ease;
}

.upload-dataset-modal .remove-icon:hover {
  background-color: rgba(255, 0, 0, 0.2);
}

.upload-dataset-modal label span {
  color: var(--btn-color);
  font-weight: 500;
}

.upload-dataset-modal label .supported-format {
  color: #808080;
  font-size: 0.875rem;
}

/* Response container styling */
.response-container {
  display: flex;
  flex-direction: column;
}

.current-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 10px 10px 10px;
  border-radius: 20px;
  gap: 20px;
}

.current-step img {
  display: flex;
  width: auto;
  height: 100px;

  mix-blend-mode: multiply;
}

.current-step.processing {
  background-color: rgb(79, 70, 229, 0.1);
  border: 1px solid rgb(79, 70, 229, 0.5);
}

.current-step.success {
  background-color: var(--success-bg);
  border: 1px solid var(--success-text);
}

.current-step p {
  margin: 0;
  color: var(--btn-color);
  font-weight: 500;
}

.current-step.success p {
  color: var(--success-text);
}

.response-message {
  padding: 10px;
  border-radius: 20px;
}

.response-message.success {
  background-color: var(--success-bg);
  border-left: 4px solid #4caf50;
  color: var(--success-text);
}

.response-message.error {
  background-color: var(--warning-bg);
  color: var(--warning-text);
  border: 1px solid rgb(255, 0, 0, 0.5);
}

.response-message p {
  margin: 0;
  font-weight: 500;
}

.upload-dataset-modal .tooltip {
  border-radius: 10px;

  position: absolute;

  z-index: 100;
}

.dataset-requirements {
  display: flex;
  flex-direction: column;
  color: white;
}

.dataset-requirements table th,
td {
  color: white;
}

.reminder a {
  color: var(--btn-color);
  font-weight: 500;
  cursor: default;
}
