.confirm-status-change-modal {
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;

  position: fixed;
  z-index: 10;
}

.confirm-status-change-modal .overlay {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  position: fixed;
  top: 0;
}

.confirm-status-change-modal .content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 40%;
  width: 40%;

  padding: 20px;
  border-radius: 40px;
  background-color: var(--primary-color);
  box-shadow: 0 0 0 5px rgb(245, 245, 249, 0.5),
    0 0 0 10px rgb(245, 245, 249, 0.5);
  position: absolute;

  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  gap: 15px;
}

.confirm-status-change-modal .content .close-btn {
  display: flex;
  height: 30px;
  width: 30px;
  padding: 5px;

  border-radius: 50%;

  background-color: var(--primary-color);
  border: 1px solid #d3d3d3;

  transition: 0.5s ease;
  cursor: pointer;

  position: absolute;
  top: 20px;
  right: 20px;
}

.confirm-status-change-modal .content .close-btn:hover {
  background-color: #808080;
}

.confirm-status-change-modal .content img:nth-child(2) {
  padding: 8px;
  height: 50px;
  width: 50px;

  background-color: rgba(255, 59, 48, 0.08);

  border-radius: 10px;
}

.confirm-status-change-modal .content p {
  font-size: 1rem;
  color: var(--secondary-dark-color);
}

.confirm-status-change-modal .content .group-buttons {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  gap: 10px;
}

.confirm-status-change-modal .content .group-buttons button:first-child {
  background-color: transparent;
  border: 1px solid #808080;
  color: var(--primary-dark-color);
}

.confirm-status-change-modal .content .group-buttons button:first-child:hover {
  background-color: rgb(211, 211, 211, 0.5);
  color: var(--primary-dark-color);
}

.confirm-status-change-modal .content .group-buttons button:last-child {
  background-color: red;
}

.confirm-status-change-modal
  .content
  .group-buttons
  button:last-child:disabled {
  cursor: default;
  background-color: #d3d3d3 !important;
  color: #808080;
}

.confirm-status-change-modal .content .group-buttons button:last-child:hover {
  background-color: rgb(255, 0, 0, 0.8);
}
