#!/usr/bin/env python
# -*- test-case-name: twisted.trial.test.test_log,twisted.trial.test.test_runner -*-

# Copyright (c) Twisted Matrix Laboratories.
# See LICENSE for details.

# fodder for test_script, which parses files for emacs local variable
# declarations.  This one is supposed to have:
#    test-case-name: twisted.trial.test.test_log,twisted.trial.test.test_runner
# in the second line
# The class declaration is irrelevant


class Foo:
    pass
